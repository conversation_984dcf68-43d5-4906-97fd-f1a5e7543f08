#include "imu_sensor.h"
#include <Arduino.h>

void IMUSensor::init() {
    Serial.println("初始化QMA6100P传感器...");

    uint8_t result = qma6100p_init();
    if (result == 0) {
        Serial.println("QMA6100P初始化成功!");
    } else {
        Serial.printf("QMA6100P初始化失败，错误代码: 0x%02X\n", result);
    }
}

// 基于官方例程的QMA6100P初始化函数
uint8_t IMUSensor::qma6100p_init(void) {
    uint8_t id = 0;
    Wire.begin(IIC_SDA, IIC_SCL, 400000); /* 初始化IIC总线 */

    qma6100p_read_reg(QMA6100P_CHIP_ID, &id, 1); /* 读取QMA6100P器件ID */
    Serial.printf("读取到的芯片ID: 0x%02X (期望: 0x%02X)\n", id, QMA6100P_DEVICE_ID);

    if (id != QMA6100P_DEVICE_ID) {
        Serial.printf("芯片ID不匹配! 读取到: 0x%02X\n", id);
        return 0xFF;
    }

    /* 软件复位 */
    qma6100p_write_reg(QMA6100P_REG_RESET, 0xb6);
    delay(5);
    qma6100p_write_reg(QMA6100P_REG_RESET, 0x00);
    delay(10);

    /* 厂家推荐的初始化序列 */
    qma6100p_write_reg(0x11, 0x80); /* 从standby到active状态(0x11寄存器<7>=1) */
    qma6100p_write_reg(0x11, 0x84);
    qma6100p_write_reg(0x4a, 0x20);
    qma6100p_write_reg(0x56, 0x01);
    qma6100p_write_reg(0x5f, 0x80);
    delay(2);
    qma6100p_write_reg(0x5f, 0x00);
    delay(10);

    /* 设置QMA6100P的配置参数(量程、输出频率、工作模式) */
    qma6100p_write_reg(QMA6100P_REG_RANGE, QMA6100P_RANGE_8G); /* 设置加速度计满量程范围(8G) */
    qma6100p_write_reg(QMA6100P_REG_BW_ODR, (uint8_t)(QMA6100P_BW_100 | QMA6100P_LPF_OFF)); /* 设置输出数据速率(100Hz) */
    qma6100p_write_reg(QMA6100P_REG_POWER_MANAGE, (uint8_t)QMA6100P_MCLK_51_2K | 0x80); /* 设置加速度的工作模式 */

    Serial.println("QMA6100P配置完成");
    return 0;
}

// QMA6100P寄存器写入函数
void IMUSensor::qma6100p_write_reg(uint8_t reg, uint8_t data) {
    Wire.beginTransmission(IMU_DEV_ADDR); /* 发送从机的7位器件地址到发送队列 */
    Wire.write(reg); /* 发送要写入从机寄存器的地址到发送队列 */
    Wire.write(data); /* 发送要写入从机寄存器的数据到发送队列 */
    Wire.endTransmission(); /* IIC发送发送队列的数据(不带参数,表示发送stop信号,结束传输) */
}

// 从QMA6100P读取N字节数据
void IMUSensor::qma6100p_read_reg(uint8_t reg, uint8_t *buf, uint16_t num) {
    uint8_t i = 0;
    Wire.beginTransmission(IMU_DEV_ADDR); /* 发送从机的7位器件地址到发送队列 */
    Wire.write(reg); /* 发送要读取从机的寄存器地址到发送队列 */
    Wire.endTransmission(0); /* IIC发送发送队列的数据(传参为0,表示重新发送一个start信号,保持IIC总线有效连接) */
    Wire.requestFrom(IMU_DEV_ADDR, num); /* 主机向从机发送数据请求,并获取到数据 */
    while (Wire.available()) /* 得到已经接收到的数据字节数 */
    {
        buf[i++] = Wire.read(); /* 到数据缓冲区读取数据 */
    }
}

// 从QMA6100P寄存器中读取原始x,y,z轴数据
void IMUSensor::qma6100p_read_raw_xyz(int16_t data[3]) {
    uint8_t databuf[6] = {0};
    int16_t raw_data[3];

    qma6100p_read_reg(QMA6100P_XOUTL, databuf, 6);
    raw_data[0] = (int16_t)(((databuf[1] << 8)) | (databuf[0]));
    raw_data[1] = (int16_t)(((databuf[3] << 8)) | (databuf[2]));
    raw_data[2] = (int16_t)(((databuf[5] << 8)) | (databuf[4]));

    data[0] = raw_data[0] >> 2;
    data[1] = raw_data[1] >> 2;
    data[2] = raw_data[2] >> 2;
}

// 计算得到加速度计的x,y,z轴数据
void IMUSensor::qma6100p_read_acc_xyz(float accdata[3]) {
    int16_t rawdata[3];
    qma6100p_read_raw_xyz(rawdata);

    // 使用8G量程，比例因子为1024
    accdata[0] = (float)(rawdata[0] * M_G) / 1024;
    accdata[1] = (float)(rawdata[1] * M_G) / 1024;
    accdata[2] = (float)(rawdata[2] * M_G) / 1024;
}

// 得到姿态解算后的欧拉角
void IMUSensor::acc_get_angle(float accl_in[3], float angle[2]) {
    float accl_data[3];
    float acc_normal, pitch, roll;

    acc_normal = sqrtf(accl_in[0] * accl_in[0] + accl_in[1] * accl_in[1] + accl_in[2] * accl_in[2]);
    accl_data[0] = accl_in[0] / acc_normal;
    accl_data[1] = accl_in[1] / acc_normal;
    accl_data[2] = accl_in[2] / acc_normal;

    pitch = -atan2f(accl_in[0], accl_in[2]);
    angle[0] = pitch * (180.0f / PI);

    acc_normal = sqrtf(accl_data[0] * accl_data[0] + accl_data[1] * accl_data[1] + accl_data[2] * accl_data[2]);
    roll = asinf((accl_data[1] / acc_normal));
    angle[1] = roll * (180.0f / PI);
}

IMUData IMUSensor::read() {
    IMUData data;
    data.timestamp = millis();

    // 使用官方例程的方法读取加速度数据
    float accdata[3];
    qma6100p_read_acc_xyz(accdata);

    // 转换为g值（官方例程已经转换为m/s²，需要转换为g）
    data.accel[0] = accdata[0] / M_G;  // X轴
    data.accel[1] = accdata[1] / M_G;  // Y轴
    data.accel[2] = accdata[2] / M_G;  // Z轴

    // QMA6100P只有加速度计，没有陀螺仪，设为0
    data.gyro[0] = 0.0f;
    data.gyro[1] = 0.0f;
    data.gyro[2] = 0.0f;

    return data;
}

bool IMUSensor::isConnected() {
    Wire.beginTransmission(IMU_DEV_ADDR);
    return (Wire.endTransmission() == 0);
}

void IMUSensor::getEulerAngles(float angles[2]) {
    float accdata[3];
    qma6100p_read_acc_xyz(accdata);
    acc_get_angle(accdata, angles);
}


