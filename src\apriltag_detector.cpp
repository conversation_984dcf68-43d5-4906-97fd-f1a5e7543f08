#include "apriltag_detector.h"
#include <Arduino.h>

AprilTagDetector apriltag_detector;

AprilTagDetector::AprilTagDetector() {
    gray_buffer = nullptr;
    image_width = 0;
    image_height = 0;
    initialized = false;
}

AprilTagDetector::~AprilTagDetector() {
    if (gray_buffer) {
        free(gray_buffer);
        gray_buffer = nullptr;
    }
}

bool AprilTagDetector::init() {
    // 为160x120图像分配灰度缓冲区
    image_width = 160;
    image_height = 120;

    gray_buffer = (uint8_t*)malloc(image_width * image_height);
    if (!gray_buffer) {
        Serial.println("AprilTag检测器内存分配失败");
        return false;
    }

    initialized = true;
    Serial.println("AprilTag检测器初始化完成");
    return true;
}

TagDetection AprilTagDetector::detect(camera_fb_t* fb) {
    TagDetection detection;
    detection.detected = false;
    detection.count = 0;
    detection.confidence = 0.0f;
    detection.center_x = 0;
    detection.center_y = 0;

    if (!initialized || !fb || !fb->buf) {
        return detection;
    }

    // 转换为灰度图像
    rgb565ToGray(fb->buf, gray_buffer, image_width, image_height);

    // 检测方形图案
    int square_count = detectSquarePatterns(gray_buffer, image_width, image_height);

    if (square_count > 0) {
        detection.detected = true;
        detection.count = square_count;
        detection.confidence = min(1.0f, square_count * 0.3f);
    }

    return detection;
}

bool AprilTagDetector::detectSimple(camera_fb_t* fb) {
    if (!initialized || !fb || !fb->buf) {
        Serial.println("AprilTag检测: 初始化失败或图像无效");
        return false;
    }

    // 转换为灰度图像
    rgb565ToGray(fb->buf, gray_buffer, image_width, image_height);

    // 方法1: 方形图案检测
    int square_count = detectSquarePatterns(gray_buffer, image_width, image_height);

    // 方法2: 边缘检测
    int edge_count = detectSimpleEdges(gray_buffer, image_width, image_height);

    // 方法3: AprilTag特征检测
    bool has_apriltag_features = hasAprilTagCharacteristics(gray_buffer, image_width, image_height);

    // 添加调试信息
    static int debug_counter = 0;
    if (debug_counter % 10 == 0) {  // 每10次检测输出一次调试信息
        Serial.printf("AprilTag检测调试: 方形=%d, 边缘=%d, 特征=%s\n",
                     square_count, edge_count, has_apriltag_features ? "是" : "否");

        // 输出一些像素值样本用于调试
        Serial.printf("像素样本: [0,0]=%d, [80,60]=%d, [159,119]=%d\n",
                     gray_buffer[0],
                     gray_buffer[60 * image_width + 80],
                     gray_buffer[(image_height-1) * image_width + (image_width-1)]);
    }
    debug_counter++;

    // 综合检测条件：必须满足特征检测，并且有足够的方形或边缘
    bool has_sufficient_squares = square_count >= 2;  // 至少2个有效方形
    bool has_strong_edges = edge_count > 15000;  // 强边缘信号

    // 只有在具备AprilTag特征的情况下才认为检测成功
    return has_apriltag_features && (has_sufficient_squares || has_strong_edges);
}

void AprilTagDetector::rgb565ToGray(const uint8_t* rgb565_data, uint8_t* gray_data, int width, int height) {
    for (int i = 0; i < width * height; i++) {
        // RGB565格式：每个像素2字节，小端序
        uint16_t pixel = rgb565_data[i*2] | ((uint16_t)rgb565_data[i*2+1] << 8);

        // 提取RGB分量
        uint8_t r = (pixel >> 11) & 0x1F;  // 5位红色
        uint8_t g = (pixel >> 5) & 0x3F;   // 6位绿色
        uint8_t b = pixel & 0x1F;          // 5位蓝色

        // 扩展到8位（优化版本）
        r = (r << 3) | (r >> 2);  // 5位扩展到8位
        g = (g << 2) | (g >> 4);  // 6位扩展到8位
        b = (b << 3) | (b >> 2);  // 5位扩展到8位

        // 转换为灰度 (使用整数运算优化)
        gray_data[i] = (uint8_t)((r * 77 + g * 150 + b * 29) >> 8);
    }
}

int AprilTagDetector::detectSquarePatterns(const uint8_t* gray_data, int width, int height) {
    int square_count = 0;
    const int min_size = 8;   // 最小方形尺寸
    const int max_size = 40;  // 最大方形尺寸
    const int step = 4;       // 检测步长

    // 在图像中搜索方形图案
    for (int y = min_size; y < height - max_size; y += step) {
        for (int x = min_size; x < width - max_size; x += step) {
            // 检测不同尺寸的方形
            for (int size = min_size; size <= max_size; size += 4) {
                if (x + size >= width || y + size >= height) break;

                if (verifyAprilTagPattern(gray_data, x, y, size, width, height)) {
                    square_count++;
                    // 跳过重叠区域
                    x += size / 2;
                    break;
                }
            }
        }
    }

    return square_count;
}

bool AprilTagDetector::verifyAprilTagPattern(const uint8_t* gray_data, int x, int y, int size, int width, int height) {
    const int border_width = size / 8;  // 边框宽度
    const int inner_size = size - 2 * border_width;  // 内部区域大小

    if (inner_size <= 0) return false;

    // 检查外边框是否为黑色
    int black_border_pixels = 0;
    int total_border_pixels = 0;

    // 检查上下边框
    for (int i = 0; i < size; i++) {
        // 上边框
        if (y * width + x + i < width * height) {
            total_border_pixels++;
            if (gray_data[y * width + x + i] < 100) black_border_pixels++;  // 放宽黑色阈值
        }
        // 下边框
        if ((y + size - 1) * width + x + i < width * height) {
            total_border_pixels++;
            if (gray_data[(y + size - 1) * width + x + i] < 100) black_border_pixels++;
        }
    }

    // 检查左右边框
    for (int i = 1; i < size - 1; i++) {
        // 左边框
        if ((y + i) * width + x < width * height) {
            total_border_pixels++;
            if (gray_data[(y + i) * width + x] < 100) black_border_pixels++;
        }
        // 右边框
        if ((y + i) * width + x + size - 1 < width * height) {
            total_border_pixels++;
            if (gray_data[(y + i) * width + x + size - 1] < 100) black_border_pixels++;
        }
    }

    // 边框黑色像素比例应该大于75% (提高要求)
    float border_ratio = (float)black_border_pixels / total_border_pixels;
    if (border_ratio < 0.75f) return false;

    // 检查内部区域是否有白色背景和黑色图案
    int white_pixels = 0;
    int black_pixels = 0;
    int total_inner_pixels = 0;

    for (int dy = border_width; dy < size - border_width; dy++) {
        for (int dx = border_width; dx < size - border_width; dx++) {
            int idx = (y + dy) * width + (x + dx);
            if (idx < width * height) {
                total_inner_pixels++;
                if (gray_data[idx] > 130) {  // 降低白色阈值
                    white_pixels++;
                } else if (gray_data[idx] < 100) {  // 放宽黑色阈值
                    black_pixels++;
                }
            }
        }
    }

    // 内部应该有足够的对比度（既有白色又有黑色）
    float white_ratio = (float)white_pixels / total_inner_pixels;
    float black_ratio = (float)black_pixels / total_inner_pixels;

    // AprilTag内部应该有明显的黑白对比 (提高要求)
    return (white_ratio > 0.2f && black_ratio > 0.2f && (white_ratio + black_ratio) > 0.65f);
}

int AprilTagDetector::detectSimpleEdges(const uint8_t* gray_data, int width, int height) {
    int edge_count = 0;
    const int threshold = 50;  // 边缘检测阈值

    // 简单的Sobel边缘检测
    for (int y = 1; y < height - 1; y++) {
        for (int x = 1; x < width - 1; x++) {
            int idx = y * width + x;

            // 计算水平和垂直梯度
            int gx = -gray_data[idx - width - 1] + gray_data[idx - width + 1] +
                     -2 * gray_data[idx - 1] + 2 * gray_data[idx + 1] +
                     -gray_data[idx + width - 1] + gray_data[idx + width + 1];

            int gy = -gray_data[idx - width - 1] - 2 * gray_data[idx - width] - gray_data[idx - width + 1] +
                     gray_data[idx + width - 1] + 2 * gray_data[idx + width] + gray_data[idx + width + 1];

            // 计算梯度幅值
            int magnitude = abs(gx) + abs(gy);  // 使用曼哈顿距离近似

            if (magnitude > threshold) {
                edge_count++;
            }
        }
    }

    return edge_count;
}

bool AprilTagDetector::hasAprilTagCharacteristics(const uint8_t* gray_data, int width, int height) {
    // 检查图像是否具有AprilTag的典型特征

    // 1. 检查黑白像素分布
    int black_pixels = 0, white_pixels = 0, gray_pixels = 0;
    int total_pixels = width * height;

    for (int i = 0; i < total_pixels; i++) {
        if (gray_data[i] < 80) {
            black_pixels++;
        } else if (gray_data[i] > 180) {
            white_pixels++;
        } else {
            gray_pixels++;
        }
    }

    float black_ratio = (float)black_pixels / total_pixels;
    float white_ratio = (float)white_pixels / total_pixels;
    float gray_ratio = (float)gray_pixels / total_pixels;

    // AprilTag应该有明显的黑白对比，灰色像素较少
    if (black_ratio < 0.15f || white_ratio < 0.15f || gray_ratio > 0.6f) {
        return false;
    }

    // 2. 检查是否有规律的方形结构（简化版）
    int regular_patterns = 0;
    for (int y = 10; y < height - 10; y += 8) {
        for (int x = 10; x < width - 10; x += 8) {
            // 检查8x8区域的对比度
            int local_black = 0, local_white = 0;
            for (int dy = 0; dy < 8; dy++) {
                for (int dx = 0; dx < 8; dx++) {
                    int idx = (y + dy) * width + (x + dx);
                    if (idx < total_pixels) {
                        if (gray_data[idx] < 100) local_black++;
                        else if (gray_data[idx] > 150) local_white++;
                    }
                }
            }

            // 如果这个8x8区域有明显的黑白对比
            if (local_black > 20 && local_white > 20) {
                regular_patterns++;
            }
        }
    }

    // 至少需要一定数量的规律模式
    return regular_patterns >= 3;
}

