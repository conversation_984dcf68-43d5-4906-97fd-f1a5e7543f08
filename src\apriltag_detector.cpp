#include "apriltag_detector.h"
#include <Arduino.h>

AprilTagDetector apriltag_detector;

AprilTagDetector::AprilTagDetector() {
    gray_buffer = nullptr;
    image_width = 0;
    image_height = 0;
    initialized = false;
}

AprilTagDetector::~AprilTagDetector() {
    if (gray_buffer) {
        free(gray_buffer);
        gray_buffer = nullptr;
    }
}

bool AprilTagDetector::init() {
    // 为160x120图像分配灰度缓冲区
    image_width = 160;
    image_height = 120;

    gray_buffer = (uint8_t*)malloc(image_width * image_height);
    if (!gray_buffer) {
        Serial.println("AprilTag检测器内存分配失败");
        return false;
    }

    initialized = true;
    Serial.println("AprilTag检测器初始化完成");
    return true;
}

TagDetection AprilTagDetector::detect(camera_fb_t* fb) {
    TagDetection detection;
    detection.detected = false;
    detection.count = 0;
    detection.confidence = 0.0f;
    detection.center_x = 0;
    detection.center_y = 0;

    if (!initialized || !fb || !fb->buf) {
        return detection;
    }

    // 转换为灰度图像
    rgb565ToGray(fb->buf, gray_buffer, image_width, image_height);

    // 检测方形图案
    int square_count = detectSquarePatterns(gray_buffer, image_width, image_height);

    if (square_count > 0) {
        detection.detected = true;
        detection.count = square_count;
        detection.confidence = min(1.0f, square_count * 0.3f);
    }

    return detection;
}

bool AprilTagDetector::detectSimple(camera_fb_t* fb) {
    if (!initialized || !fb || !fb->buf) {
        return false;
    }

    // 转换为灰度图像
    rgb565ToGray(fb->buf, gray_buffer, image_width, image_height);

    // 简单的方形图案检测
    int square_count = detectSquarePatterns(gray_buffer, image_width, image_height);

    return square_count > 0;
}

void AprilTagDetector::rgb565ToGray(const uint8_t* rgb565_data, uint8_t* gray_data, int width, int height) {
    for (int i = 0; i < width * height; i++) {
        // RGB565格式：每个像素2字节，RRRRR GGGGGG BBBBB
        uint16_t pixel = ((uint16_t)rgb565_data[i*2+1] << 8) | rgb565_data[i*2];

        // 提取RGB分量
        uint8_t r = (pixel >> 11) & 0x1F;  // 5位红色
        uint8_t g = (pixel >> 5) & 0x3F;   // 6位绿色
        uint8_t b = pixel & 0x1F;          // 5位蓝色

        // 扩展到8位
        r = (r * 255) / 31;
        g = (g * 255) / 63;
        b = (b * 255) / 31;

        // 转换为灰度 (使用标准权重)
        gray_data[i] = (uint8_t)(0.299f * r + 0.587f * g + 0.114f * b);
    }
}

int AprilTagDetector::detectSquarePatterns(const uint8_t* gray_data, int width, int height) {
    int square_count = 0;
    const int min_size = 8;   // 最小方形尺寸
    const int max_size = 40;  // 最大方形尺寸
    const int step = 4;       // 检测步长

    // 在图像中搜索方形图案
    for (int y = min_size; y < height - max_size; y += step) {
        for (int x = min_size; x < width - max_size; x += step) {
            // 检测不同尺寸的方形
            for (int size = min_size; size <= max_size; size += 4) {
                if (x + size >= width || y + size >= height) break;

                if (verifyAprilTagPattern(gray_data, x, y, size, width, height)) {
                    square_count++;
                    // 跳过重叠区域
                    x += size / 2;
                    break;
                }
            }
        }
    }

    return square_count;
}

bool AprilTagDetector::verifyAprilTagPattern(const uint8_t* gray_data, int x, int y, int size, int width, int height) {
    const int border_width = size / 8;  // 边框宽度
    const int inner_size = size - 2 * border_width;  // 内部区域大小

    if (inner_size <= 0) return false;

    // 检查外边框是否为黑色
    int black_border_pixels = 0;
    int total_border_pixels = 0;

    // 检查上下边框
    for (int i = 0; i < size; i++) {
        // 上边框
        if (y * width + x + i < width * height) {
            total_border_pixels++;
            if (gray_data[y * width + x + i] < 80) black_border_pixels++;
        }
        // 下边框
        if ((y + size - 1) * width + x + i < width * height) {
            total_border_pixels++;
            if (gray_data[(y + size - 1) * width + x + i] < 80) black_border_pixels++;
        }
    }

    // 检查左右边框
    for (int i = 1; i < size - 1; i++) {
        // 左边框
        if ((y + i) * width + x < width * height) {
            total_border_pixels++;
            if (gray_data[(y + i) * width + x] < 80) black_border_pixels++;
        }
        // 右边框
        if ((y + i) * width + x + size - 1 < width * height) {
            total_border_pixels++;
            if (gray_data[(y + i) * width + x + size - 1] < 80) black_border_pixels++;
        }
    }

    // 边框黑色像素比例应该大于70%
    float border_ratio = (float)black_border_pixels / total_border_pixels;
    if (border_ratio < 0.7f) return false;

    // 检查内部区域是否有白色背景和黑色图案
    int white_pixels = 0;
    int black_pixels = 0;
    int total_inner_pixels = 0;

    for (int dy = border_width; dy < size - border_width; dy++) {
        for (int dx = border_width; dx < size - border_width; dx++) {
            int idx = (y + dy) * width + (x + dx);
            if (idx < width * height) {
                total_inner_pixels++;
                if (gray_data[idx] > 150) {
                    white_pixels++;
                } else if (gray_data[idx] < 80) {
                    black_pixels++;
                }
            }
        }
    }

    // 内部应该有足够的对比度（既有白色又有黑色）
    float white_ratio = (float)white_pixels / total_inner_pixels;
    float black_ratio = (float)black_pixels / total_inner_pixels;

    // AprilTag内部应该有明显的黑白对比
    return (white_ratio > 0.2f && black_ratio > 0.2f && (white_ratio + black_ratio) > 0.6f);
}

