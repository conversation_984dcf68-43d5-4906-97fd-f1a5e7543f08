[env:dnesp32s3]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino
monitor_speed = 115200

; 正点原子DNESP32S3开发板配置
; 主控: ATK-MWS3S (对应ESP32S3-WOOD-N16R8)
; FLASH: 16MB, PSRAM: 8MB
board_build.flash_size = 16MB
board_build.psram_type = opi
board_build.memory_type = opi_opi

lib_deps =
    Wire
    espressif/esp32-camera@^2.0.4

build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DCAMERA_MODEL_CUSTOM
    ; 正点原子DNESP32S3开发板配置
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DARDUINO_USB_MSC_ON_BOOT=0
    -DARDUINO_USB_DFU_ON_BOOT=0
    ; OV2640摄像头配置
    -DCAMERA_MODEL_OV2640
    ; QMA6100P三轴加速度计配置
    -DUSE_QMA6100P

upload_speed = 921600
monitor_filters = esp32_exception_decoder

; CH340 USB转TTL配置
upload_port = COM5
monitor_port = COM5
