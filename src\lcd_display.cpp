#include "lcd_display.h"
#include "font.h"

LCDDisplay lcd_display;

// 全局变量
extern const uint8_t font_1608[][16];  // 字体数据
uint16_t spilcd_width = 240;   // 屏幕宽度
uint16_t spilcd_height = 240;  // 屏幕高度

void LCDDisplay::init() {
    Serial.println("初始化LCD显示屏...");

    // 设置LCD尺寸
#if SPI_LCD_TYPE
    lcd_width = 240;   // 2.4寸屏幕宽度
    lcd_height = 320;  // 2.4寸屏幕高度
#else
    lcd_width = 240;   // 1.3寸屏幕宽度
    lcd_height = 240;  // 1.3寸屏幕高度
#endif

    // 初始化XL9555 IO扩展芯片
    xl9555_init();

    // 初始化LCD需要用到的引脚
    xl9555_io_config(SLCD_PWR, IO_SET_OUTPUT);
    xl9555_io_config(SLCD_RST, IO_SET_OUTPUT);
    pinMode(SLCD_WR_PIN, OUTPUT);

    // LCD引脚默认状态为高电平
    xl9555_pin_set(SLCD_PWR, IO_SET_HIGH);
    xl9555_pin_set(SLCD_RST, IO_SET_HIGH);
    digitalWrite(SLCD_WR_PIN, HIGH);

    // 配置SPI (使用默认SPI)
    spi_lcd = new SPIClass();
    spi_lcd->begin(SLCD_SCK_PIN, SLCD_SDI_PIN, SLCD_SDA_PIN, SLCD_CS_PIN);
    pinMode(SLCD_CS_PIN, OUTPUT);

    // 硬件复位
    xl9555_pin_set(SLCD_RST, IO_SET_HIGH);
    delay(10);
    xl9555_pin_set(SLCD_RST, IO_SET_LOW);
    delay(10);
    xl9555_pin_set(SLCD_RST, IO_SET_HIGH);
    delay(120);

    Serial.println("开始配置LCD寄存器...");

    spi_lcd->beginTransaction(SPISettings(SPICLK, MSBFIRST, SPI_MODE3));
    digitalWrite(SLCD_CS_PIN, LOW);  // 选中LCD

    // 配置LCD寄存器（1.3寸ST7789V，基于官方例程）
    lcd_write_cmd(0x11);  // Sleep Out
    delay(120);

#if SPI_LCD_TYPE  // 2.4寸LCD配置
    // 2.4寸配置代码...
#else  // 1.3寸LCD配置
    lcd_write_cmd(0x36);  // Memory Data Access Control
    lcd_write_data(0x00);
    lcd_write_cmd(0x3A);  // RGB 5-6-5-bit
    lcd_write_data(0x65);
    lcd_write_cmd(0xB2);  // Porch Setting
    lcd_write_data(0x0C);
    lcd_write_data(0x0C);
    lcd_write_data(0x00);
    lcd_write_data(0x33);
    lcd_write_data(0x33);
    lcd_write_cmd(0xB7);  // Gate Control
    lcd_write_data(0x72);
    lcd_write_cmd(0xBB);  // VCOM Setting
    lcd_write_data(0x3D);
    lcd_write_cmd(0xC0);  // LCM Control
    lcd_write_data(0x2C);
    lcd_write_cmd(0xC2);  // VDV and VRH Command Enable
    lcd_write_data(0x01);
    lcd_write_cmd(0xC3);  // VRH Set
    lcd_write_data(0x19);
    lcd_write_cmd(0xC4);  // VDV Set
    lcd_write_data(0x20);
    lcd_write_cmd(0xC6);  // Frame Rate Control in Normal Mode
    lcd_write_data(0x0F);
    lcd_write_cmd(0xD0);  // Power Control 1
    lcd_write_data(0xA4);
    lcd_write_data(0xA1);
    lcd_write_cmd(0xE0);  // Positive Voltage Gamma Control
    lcd_write_data(0xD0);
    lcd_write_data(0x04);
    lcd_write_data(0x0D);
    lcd_write_data(0x11);
    lcd_write_data(0x13);
    lcd_write_data(0x2B);
    lcd_write_data(0x3F);
    lcd_write_data(0x54);
    lcd_write_data(0x4C);
    lcd_write_data(0x18);
    lcd_write_data(0x0D);
    lcd_write_data(0x0B);
    lcd_write_data(0x1F);
    lcd_write_data(0x23);
    lcd_write_cmd(0xE1);  // Negative Voltage Gamma Control
    lcd_write_data(0xD0);
    lcd_write_data(0x04);
    lcd_write_data(0x0C);
    lcd_write_data(0x11);
    lcd_write_data(0x13);
    lcd_write_data(0x2C);
    lcd_write_data(0x3F);
    lcd_write_data(0x44);
    lcd_write_data(0x51);
    lcd_write_data(0x2F);
    lcd_write_data(0x1F);
    lcd_write_data(0x1F);
    lcd_write_data(0x20);
    lcd_write_data(0x23);
    lcd_write_cmd(0x21);  // Display Inversion On
    lcd_write_cmd(0x29);  // Display On
#endif

    digitalWrite(SLCD_CS_PIN, HIGH);  // 取消选中
    spi_lcd->endTransaction();

    // 清屏并测试显示
    Serial.println("开始LCD显示测试...");

    Serial.println("测试1: 清屏黑色");
    clear(BLACK);
    delay(1000);

    Serial.println("测试2: 全屏红色");
    clear(RED);
    delay(1000);

    Serial.println("测试3: 全屏绿色");
    clear(GREEN);
    delay(1000);

    Serial.println("测试4: 全屏蓝色");
    clear(BLUE);
    delay(1000);

    Serial.println("测试5: 回到黑色");
    clear(BLACK);
    delay(500);

    Serial.println("测试6: 绘制像素点");
    // 测试绘制一些像素点
    for (int i = 0; i < 50; i++) {
        draw_point(i, i, WHITE);
        draw_point(i, 50-i, YELLOW);
    }

    Serial.println("测试7: 尝试开启背光");
    // 尝试开启LCD背光
    xl9555_pin_set(SLCD_PWR, IO_SET_HIGH);  // 尝试开启背光

    Serial.println("LCD显示屏初始化完成");
}

// LCD底层驱动函数
void LCDDisplay::lcd_write_cmd(uint8_t cmd) {
    digitalWrite(SLCD_WR_PIN, LOW);  // 命令模式
    spi_lcd->transfer(cmd);
}

void LCDDisplay::lcd_write_data(uint8_t data) {
    digitalWrite(SLCD_WR_PIN, HIGH);  // 数据模式
    spi_lcd->transfer(data);
}

void LCDDisplay::lcd_write_bytes(uint8_t *data, uint32_t size) {
    digitalWrite(SLCD_WR_PIN, HIGH);  // 数据模式
    spi_lcd->transfer(data, size);
}

void LCDDisplay::lcd_write_pixeldata(uint16_t data) {
    digitalWrite(SLCD_WR_PIN, HIGH);  // 数据模式
    spi_lcd->transfer16(data);
}

void LCDDisplay::lcd_set_address(uint16_t xs, uint16_t ys, uint16_t xe, uint16_t ye) {
    lcd_write_cmd(0x2A);  // 列地址设置
    lcd_write_data((uint8_t)(xs >> 8) & 0xFF);
    lcd_write_data((uint8_t)xs & 0xFF);
    lcd_write_data((uint8_t)(xe >> 8) & 0xFF);
    lcd_write_data((uint8_t)xe & 0xFF);

    lcd_write_cmd(0x2B);  // 行地址设置
    lcd_write_data((uint8_t)(ys >> 8) & 0xFF);
    lcd_write_data((uint8_t)ys & 0xFF);
    lcd_write_data((uint8_t)(ye >> 8) & 0xFF);
    lcd_write_data((uint8_t)ye & 0xFF);

    lcd_write_cmd(0x2C);  // 开始写入显存
}

void LCDDisplay::clear(uint16_t color) {
    fill(0, 0, lcd_width - 1, lcd_height - 1, color);
}

void LCDDisplay::draw_point(uint16_t x, uint16_t y, uint16_t color) {
    spi_lcd->beginTransaction(SPISettings(SPICLK, MSBFIRST, SPI_MODE3));
    digitalWrite(SLCD_CS_PIN, LOW);
    lcd_set_address(x, y, x, y);
    lcd_write_pixeldata(color);
    digitalWrite(SLCD_CS_PIN, HIGH);
    spi_lcd->endTransaction();
}

void LCDDisplay::fill(uint16_t xs, uint16_t ys, uint16_t xe, uint16_t ye, uint16_t color) {
    spi_lcd->beginTransaction(SPISettings(SPICLK, MSBFIRST, SPI_MODE3));
    digitalWrite(SLCD_CS_PIN, LOW);
    lcd_set_address(xs, ys, xe, ye);

    uint32_t size = (xe - xs + 1) * (ye - ys + 1);
    for (uint32_t i = 0; i < size; i++) {
        lcd_write_pixeldata(color);
    }

    digitalWrite(SLCD_CS_PIN, HIGH);
    spi_lcd->endTransaction();
}

// 字符串显示函数（基于官方例程）
void LCDDisplay::show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                            lcd_font_t font, const char* text, uint16_t color) {
    uint16_t x0 = x;
    uint8_t char_width;
    uint8_t char_height;

    // 根据字体获取字符尺寸
    switch (font) {
#if (FONT_16 != 0)
        case LCD_FONT_16:
            char_width = FONT_16_CHAR_WIDTH;
            char_height = FONT_16_CHAR_HEIGHT;
            break;
#endif
        default:
            return;
    }

    while (*text != '\0') {
        if (x > (x0 + width - char_width)) {
            // 换行
            x = x0;
            y += char_height;
            if (y > (y + height - char_height)) {
                break;  // 超出显示区域
            }
        }

        // 显示单个字符
        show_char(x, y, *text, font, 0, color);
        x += char_width;
        text++;
    }
}

// 字符显示函数（基于官方例程的正确实现）
void LCDDisplay::show_char(uint16_t x, uint16_t y, char ch, lcd_font_t font, uint8_t mode, uint16_t color) {
    const uint8_t *ch_code;
    uint8_t ch_width;
    uint8_t ch_height;
    uint8_t ch_size;
    uint8_t ch_offset;
    uint8_t byte_index;
    uint8_t byte_code;
    uint8_t bit_index;
    uint8_t width_index = 0;
    uint8_t height_index = 0;

    // 只支持ASCII 32-126范围的字符
    if (ch < 32 || ch > 126) return;

    ch_offset = ch - 32;  // 得到偏移后的值（ASCII字库是从空格开始取模，所以-32就是对应字符的字库）

    // 根据字体获取字符参数
    switch (font) {
#if (FONT_16 != 0)
        case LCD_FONT_16: {
            if (ch_offset >= 95) return;  // 超出范围
            ch_code = font_1608[ch_offset];
            ch_width = FONT_16_CHAR_WIDTH;
            ch_height = FONT_16_CHAR_HEIGHT;
            ch_size = FONT_16_CHAR_SIZE;
            break;
        }
#endif
        default: {
            return;
        }
    }

    // 检查显示边界
    if ((x + ch_width > lcd_width) || (y + ch_height > lcd_height)) {
        return;
    }

    // 显示字符（按照官方例程的算法）
    for (byte_index = 0; byte_index < ch_size; byte_index++) {
        byte_code = ch_code[byte_index]; /* 获取字符的点阵数据 */

        for (bit_index = 0; bit_index < 8; bit_index++) /* 一个字节8个点 */ {
            if ((byte_code & 0x80) != 0) /* 有效点,需要显示 */ {
                draw_point(x + width_index, y + height_index, color); /* 画点出来,要显示这个点 */
            } else if (mode == 0) {
                draw_point(x + width_index, y + height_index, g_back_color); /* 画背景色,相当于这个点不显示(注意背景色由全局变量控制) */
            }

            height_index++;
            if (height_index == ch_height) /* 显示完一列了? */ {
                height_index = 0; /* y坐标复位 */
                width_index++;    /* x坐标递增 */
                break;
            }
            byte_code <<= 1; /* 移位, 以便获取下一个位的状态 */
        }
    }
}

void LCDDisplay::show_float(uint16_t x, uint16_t y, const char* label, float value, uint16_t color) {
    char buf[32];
    snprintf(buf, sizeof(buf), "%s%.3f", label, value);
    show_string(x, y, 160, 16, LCD_FONT_16, buf, color);
}

void LCDDisplay::updateDisplay(float acc[3], float angles[2], bool camera_ok, bool apriltag_detected) {
    // 简化显示，减少每次更新的内容
    static bool first_update = true;

    if (first_update) {
        // 第一次显示时清屏并显示固定标题
        clear(BLACK);
        show_string(10, 10, 220, 16, LCD_FONT_16, "DNESP32S3 AprilTag", WHITE);
        show_string(10, 30, 220, 16, LCD_FONT_16, "IMU & Camera Test", WHITE);
        first_update = false;
    }

    // 只更新数据区域，不重复显示标题
    // 清除数据显示区域
    fill(10, 60, 230, 220, BLACK);

    // 显示AprilTag检测结果（最重要的信息放在最上面）
    show_string(10, 60, 200, 16, LCD_FONT_16, "AprilTag:", YELLOW);
    if (apriltag_detected) {
        show_string(100, 60, 100, 16, LCD_FONT_16, "true", GREEN);
    } else {
        show_string(100, 60, 100, 16, LCD_FONT_16, "false", RED);
    }

    // 显示加速度数据
    show_string(10, 90, 200, 16, LCD_FONT_16, "Accel(g):", CYAN);
    show_float(10, 110, "X:", acc[0], WHITE);
    show_float(10, 130, "Y:", acc[1], WHITE);
    show_float(10, 150, "Z:", acc[2], WHITE);

    // 显示姿态角度
    show_string(10, 180, 200, 16, LCD_FONT_16, "Angles:", MAGENTA);
    show_float(10, 200, "P:", angles[0], WHITE);
    show_float(10, 220, "R:", angles[1], WHITE);

    // 显示AprilTag检测状态
    if (apriltag_detected) {
        show_string(120, 200, 100, 16, LCD_FONT_16, "Tag:YES", GREEN);
    } else {
        show_string(120, 200, 100, 16, LCD_FONT_16, "Tag:NO", RED);
    }
}

void LCDDisplay::displayCameraImage(camera_fb_t* fb) {
    if (!fb || !fb->buf) {
        // 显示错误信息
        clear(BLACK);
        show_string(50, 100, 140, 16, LCD_FONT_16, "Camera Error", RED);
        Serial.println("LCD: 摄像头数据无效");
        return;
    }

    Serial.printf("LCD: 开始显示图像 %dx%d, 格式=%d, 大小=%d\n",
                  fb->width, fb->height, fb->format, fb->len);

    // 清屏
    clear(BLACK);

    // 显示图像信息
    char info[50];
    snprintf(info, sizeof(info), "Image: %dx%d", fb->width, fb->height);
    show_string(10, 10, 220, 16, LCD_FONT_16, info, WHITE);

    // 显示图像预览（缩放到适合LCD的尺寸）
    Serial.println("LCD: 开始显示图像预览");
    displayImagePreview(fb, 20, 40, 200, 150);
    Serial.println("LCD: 图像预览显示完成");

    // 显示图像质量信息
    snprintf(info, sizeof(info), "Size: %d bytes", fb->len);
    show_string(10, 200, 220, 16, LCD_FONT_16, info, CYAN);

    snprintf(info, sizeof(info), "Format: %s", fb->format == PIXFORMAT_RGB565 ? "RGB565" : "Other");
    show_string(10, 220, 220, 16, LCD_FONT_16, info, YELLOW);
}

void LCDDisplay::displayImagePreview(camera_fb_t* fb, uint16_t x, uint16_t y, uint16_t w, uint16_t h) {
    if (!fb || !fb->buf) {
        Serial.println("LCD: 图像数据为空");
        fill(x, y, x + w, y + h, RED);
        show_string(x + 10, y + h/2, w - 20, 16, LCD_FONT_16, "No Data", WHITE);
        return;
    }

    if (fb->format != PIXFORMAT_RGB565) {
        Serial.printf("LCD: 不支持的图像格式: %d\n", fb->format);
        fill(x, y, x + w, y + h, BLUE);
        show_string(x + 10, y + h/2, w - 20, 16, LCD_FONT_16, "Bad Format", WHITE);
        return;
    }

    Serial.printf("LCD: 显示区域 (%d,%d) 到 (%d,%d), 缩放 %.2fx%.2f\n",
                  x, y, x+w-1, y+h-1, (float)fb->width/w, (float)fb->height/h);

    // 计算缩放比例
    float scale_x = (float)fb->width / w;
    float scale_y = (float)fb->height / h;

    // 设置显示区域
    lcd_set_address(x, y, x + w - 1, y + h - 1);

    // 获取图像数据指针
    uint8_t* raw_data = fb->buf;

    // 显示前几个像素用于调试
    Serial.printf("LCD: 前几个像素值: [0]=%02X%02X [1]=%02X%02X [2]=%02X%02X\n",
                  raw_data[1], raw_data[0], raw_data[3], raw_data[2], raw_data[5], raw_data[4]);

    // 逐像素显示（简单的最近邻缩放）
    for (uint16_t dy = 0; dy < h; dy++) {
        for (uint16_t dx = 0; dx < w; dx++) {
            // 计算源图像坐标
            uint16_t src_x = (uint16_t)(dx * scale_x);
            uint16_t src_y = (uint16_t)(dy * scale_y);

            // 确保不越界
            if (src_x >= fb->width) src_x = fb->width - 1;
            if (src_y >= fb->height) src_y = fb->height - 1;

            // 计算像素在缓冲区中的位置
            uint32_t pixel_index = (src_y * fb->width + src_x) * 2;  // RGB565每像素2字节

            // 读取RGB565像素值（注意字节序）
            uint16_t pixel = raw_data[pixel_index] | (raw_data[pixel_index + 1] << 8);

            // 写入LCD
            lcd_write_pixeldata(pixel);
        }

        // 每10行输出一次进度
        if (dy % 10 == 0) {
            Serial.printf("LCD: 显示进度 %d/%d\n", dy, h);
        }
    }

    Serial.println("LCD: 图像显示完成");
}

void LCDDisplay::displayTestPattern() {
    clear(BLACK);

    // 显示标题
    show_string(10, 10, 220, 16, LCD_FONT_16, "Camera Test Pattern", WHITE);

    // 显示彩色条纹测试图案
    uint16_t colors[] = {RED, GREEN, BLUE, YELLOW, CYAN, MAGENTA, WHITE};
    int num_colors = sizeof(colors) / sizeof(colors[0]);

    int stripe_width = 200 / num_colors;

    for (int i = 0; i < num_colors; i++) {
        fill(20 + i * stripe_width, 40, 20 + (i + 1) * stripe_width, 90, colors[i]);
    }

    // 显示渐变测试
    for (int x = 0; x < 200; x++) {
        uint16_t gray_level = (x * 31) / 200;  // 0-31的灰度级别
        uint16_t gray_color = (gray_level << 11) | (gray_level << 6) | gray_level;  // RGB565灰度
        fill(20 + x, 100, 21 + x, 150, gray_color);
    }

    // 显示说明文字
    show_string(10, 160, 220, 16, LCD_FONT_16, "Color Stripes", WHITE);
    show_string(10, 180, 220, 16, LCD_FONT_16, "Gray Gradient", WHITE);
    show_string(10, 200, 220, 16, LCD_FONT_16, "LCD Test OK", GREEN);
}
