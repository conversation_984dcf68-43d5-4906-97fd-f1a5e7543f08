#ifndef LCD_DISPLAY_H
#define LCD_DISPLAY_H

#include <Arduino.h>
#include <SPI.h>
#include "xl9555.h"
#include "font.h"
#include "esp_camera.h"

// LCD引脚定义 - 基于官方例程
#define SLCD_CS_PIN  21   // SPI CS引脚
#define SLCD_SDA_PIN 11   // SPI MOSI引脚
#define SLCD_SCK_PIN 12   // SPI SCK引脚
#define SLCD_SDI_PIN -1   // SPI MISO引脚（未使用）
#define SLCD_WR_PIN  40   // DC引脚（数据/命令选择）

// XL9555控制的引脚已在xl9555.h中定义
// #define SLCD_PWR 和 #define SLCD_RST 已在xl9555.h中定义为XL_PIN_P13和XL_PIN_P12

// SPI时钟频率
#define SPICLK       40000000  // 40MHz

// LCD类型选择
#define SPI_LCD_TYPE 0    // 1：2.4寸SPILCD，0：1.3寸SPILCD（根据您的说明）

// 颜色定义（RGB565格式）
#define BLACK    0x0000
#define BLUE     0x001F
#define RED      0xF800
#define GREEN    0x07E0
#define CYAN     0x07FF
#define MAGENTA  0xF81F
#define YELLOW   0xFFE0
#define WHITE    0xFFFF

// 字体大小（移除这个定义，使用font.h中的枚举）

class LCDDisplay {
private:
    SPIClass *spi_lcd;
    uint16_t lcd_width;
    uint16_t lcd_height;

    // 底层驱动函数
    void lcd_write_cmd(uint8_t cmd);
    void lcd_write_data(uint8_t data);
    void lcd_write_bytes(uint8_t *data, uint32_t size);
    void lcd_write_pixeldata(uint16_t data);
    void lcd_set_address(uint16_t xs, uint16_t ys, uint16_t xe, uint16_t ye);

public:
    void init();
    void clear(uint16_t color = BLACK);
    void draw_point(uint16_t x, uint16_t y, uint16_t color);
    void fill(uint16_t xs, uint16_t ys, uint16_t xe, uint16_t ye, uint16_t color);
    void show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                     lcd_font_t font, const char* text, uint16_t color);
    void show_char(uint16_t x, uint16_t y, char ch, lcd_font_t font, uint8_t mode, uint16_t color);
    void show_float(uint16_t x, uint16_t y, const char* label, float value, uint16_t color = WHITE);
    void updateDisplay(float acc[3], float angles[2], bool camera_ok = false, bool apriltag_detected = false);
    void displayCameraImage(camera_fb_t* fb);  // 显示摄像头图像
    void displayImagePreview(camera_fb_t* fb, uint16_t x, uint16_t y, uint16_t w, uint16_t h);  // 显示图像预览
    void displayTestPattern();  // 显示测试图案
};

extern LCDDisplay lcd_display;

#endif
