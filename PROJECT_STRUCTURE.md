# ESP32S3 AprilTag检测系统 - 项目结构

## 📁 项目文件树

```
AprilTag_ESP32S3/
├── .gitignore                    # Git忽略文件配置
├── .vscode/                      # VSCode开发环境配置
│   ├── extensions.json           # 推荐扩展插件
│   └── settings.json             # 编辑器设置
├── platformio.ini                # PlatformIO项目配置文件
├── PROJECT_STRUCTURE.md          # 项目结构说明文档 (本文件)
└── src/                          # 源代码目录
    ├── main.cpp                  # 主程序入口文件
    ├── apriltag_detector.h       # AprilTag检测器头文件
    ├── apriltag_detector.cpp     # AprilTag检测器实现
    ├── imu_sensor.h              # IMU传感器驱动头文件
    ├── imu_sensor.cpp            # IMU传感器驱动实现
    ├── lcd_display.h             # LCD显示驱动头文件
    ├── lcd_display.cpp           # LCD显示驱动实现
    ├── sensor_fusion.h           # 传感器融合头文件
    ├── sensor_fusion.cpp         # 传感器融合实现
    ├── xl9555.h                  # XL9555 IO扩展芯片头文件
    ├── xl9555.cpp                # XL9555 IO扩展芯片实现
    ├── font.h                    # 字体数据头文件
    ├── font.cpp                  # 字体数据实现
    ├── altitude_sensor.h         # 气压计传感器头文件 (预留)
    └── altitude_sensor.cpp       # 气压计传感器实现 (预留)
```

## 📋 文件功能说明

### 🔧 配置文件
| 文件 | 功能描述 |
|------|----------|
| `.gitignore` | Git版本控制忽略文件配置 |
| `platformio.ini` | PlatformIO项目配置，包含开发板、库依赖等 |
| `.vscode/extensions.json` | VSCode推荐扩展插件列表 |
| `.vscode/settings.json` | VSCode编辑器设置和代码格式化配置 |

### 💻 核心源码
| 文件 | 功能描述 | 状态 |
|------|----------|------|
| `main.cpp` | 系统主程序，负责初始化和主循环 | ✅ 完成 |
| `apriltag_detector.*` | AprilTag检测算法，包含图像处理和标签识别 | ✅ 完成 |
| `imu_sensor.*` | QMA6100P三轴加速度计驱动和姿态解算 | ✅ 完成 |
| `lcd_display.*` | ST7789 TFT显示屏驱动和界面显示 | ✅ 完成 |
| `sensor_fusion.*` | 多传感器数据融合算法框架 | 🔄 基础版 |
| `xl9555.*` | XL9555 GPIO扩展芯片驱动 | ✅ 完成 |
| `font.*` | LCD显示用字体数据和字符渲染 | ✅ 完成 |
| `altitude_sensor.*` | 气压计传感器驱动接口 | 📋 预留 |

## 🏗️ 系统架构

### 硬件抽象层 (HAL)
- **摄像头驱动**: OV2640图像采集
- **显示驱动**: ST7789 LCD控制
- **传感器驱动**: QMA6100P IMU数据读取
- **IO扩展**: XL9555 GPIO控制

### 算法处理层
- **图像处理**: RGB565转灰度、边缘检测
- **AprilTag检测**: 方形识别、特征验证
- **姿态解算**: 加速度计数据处理
- **传感器融合**: 多源数据整合

### 应用层
- **系统初始化**: 硬件配置和模块启动
- **实时监控**: 状态显示和数据输出
- **主控逻辑**: 检测流程和结果处理

## 📊 代码统计

- **总文件数**: 19个
- **总代码行数**: 1,771行
- **头文件**: 9个
- **实现文件**: 8个
- **配置文件**: 4个

## 🔄 更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.1 | 2025-07-29 | 添加项目结构说明文档 |
| v1.0 | 2025-07-29 | 初始版本，完成基础功能模块 |

## 📝 文档维护指南

### 添加新文件时

1. 在文件树中添加新文件路径和注释
2. 在功能说明表格中添加文件描述
3. 更新代码统计信息
4. 在更新记录中添加版本信息

### 删除文件时

1. 从文件树中移除对应条目
2. 从功能说明表格中删除对应行
3. 更新代码统计信息
4. 记录删除原因

### 修改文件功能时

1. 更新功能说明表格中的描述
2. 如有必要，更新状态标识
3. 在更新记录中说明变更内容

---

> 📝 **说明**: 此文档需要手动维护，确保与实际项目结构保持同步。
> 🔗 **维护**: 每次文件结构变化时请及时更新此文档。
> 💡 **提示**: 可以使用AI助手帮助快速更新文档内容。
