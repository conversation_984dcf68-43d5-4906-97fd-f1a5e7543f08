#ifndef __FONT_H
#define __FONT_H

#include <Arduino.h>

/* 常用ASCII表
 * 偏移量32
 * ASCII字符集: !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abcdefghijklmnopqrstuvwxyz{|}~
 * PC2LCD2002取模方式设置：阴码+逐列式+顺向+C51格式
 * 总共：4个字符集（12*12、16*16、24*24和32*32），用户可以自行新增其他分辨率的字符集。
 * 每个字符所占用的字节数为:(size/8+((size%8)?1:0))*(size/2),其中size:是字库生成时的点阵大小(12/16/24/32...)
 */

// 字体类型定义
typedef enum {
    LCD_FONT_12 = 12,
    LCD_FONT_16 = 16,
    LCD_FONT_24 = 24,
    LCD_FONT_32 = 32
} lcd_font_t;

// 字体使能开关
#define FONT_12 0  // 暂时禁用以节省空间
#define FONT_16 1  // 启用16号字体
#define FONT_24 0  // 暂时禁用大字体以节省空间
#define FONT_32 0

#if (FONT_12 != 0)
#define FONT_12_CHAR_WIDTH   6
#define FONT_12_CHAR_HEIGHT  12
#define FONT_12_CHAR_SIZE    12

/* 12*6 ASCII字符点阵 */
extern const uint8_t font_1206[95][FONT_12_CHAR_SIZE];
#endif  /* FONT_12 */

#if (FONT_16 != 0)
#define FONT_16_CHAR_WIDTH   8
#define FONT_16_CHAR_HEIGHT  16
#define FONT_16_CHAR_SIZE    16

/* 16*8 ASCII字符点阵 */
extern const uint8_t font_1608[95][FONT_16_CHAR_SIZE];
#endif /* FONT_16 */

#if (FONT_24 != 0)
#define FONT_24_CHAR_WIDTH   12
#define FONT_24_CHAR_HEIGHT  24
#define FONT_24_CHAR_SIZE    48

/* 24*12 ASCII字符点阵 */
extern const uint8_t font_2412[95][FONT_24_CHAR_SIZE];
#endif /* FONT_24 */

#if (FONT_32 != 0)
#define FONT_32_CHAR_WIDTH   16
#define FONT_32_CHAR_HEIGHT  32
#define FONT_32_CHAR_SIZE    64

/* 32*16 ASCII字符点阵 */
extern const uint8_t font_3216[95][FONT_32_CHAR_SIZE];
#endif /* FONT_32 */

// 全局变量声明
extern uint16_t spilcd_width;
extern uint16_t spilcd_height;
extern uint16_t g_back_color;

// 字体函数声明
void lcd_show_char(uint16_t x, uint16_t y, char ch, lcd_font_t font, uint8_t mode, uint16_t color);
void lcd_show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                     lcd_font_t font, const char* str, uint16_t color);

#endif
