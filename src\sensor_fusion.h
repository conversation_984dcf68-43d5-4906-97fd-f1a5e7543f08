#ifndef SENSOR_FUSION_H
#define SENSOR_FUSION_H

#include "imu_sensor.h"
#include "apriltag_detector.h"

struct Position {
    float x, y, z;
    float confidence;
};

class SensorFusion {
public:
    void init();
    Position update(TagDetection& tag, IMUData& imu, float altitude);
    
private:
    void predict(IMUData& imu, float dt);
    void updateVision(TagDetection& tag);
    void updateAltitude(float altitude);
    
    float state[6];  // x, y, z, vx, vy, vz
    float P[36];     // 协方差矩阵
};

#endif


