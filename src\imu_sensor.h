#ifndef IMU_SENSOR_H
#define IMU_SENSOR_H

#include <Arduino.h>
#include <Wire.h>

// QMA6100P三轴加速度计配置 - 基于官方例程
// I2C地址和引脚定义
#define IMU_DEV_ADDR         0x12  // QMA6100P I2C地址
#define IIC_SDA              41    // I2C SDA引脚
#define IIC_SCL              42    // I2C SCL引脚

// 寄存器地址定义 - 基于官方例程
#define QMA6100P_CHIP_ID     0x00  // 芯片ID寄存器
#define QMA6100P_DEVICE_ID   0x90  // 期望的设备ID值
#define QMA6100P_XOUTL       0x01  // X轴数据低字节
#define QMA6100P_XOUTH       0x02  // X轴数据高字节
#define QMA6100P_YOUTL       0x03  // Y轴数据低字节
#define QMA6100P_YOUTH       0x04  // Y轴数据高字节
#define QMA6100P_ZOUTL       0x05  // Z轴数据低字节
#define QMA6100P_ZOUTH       0x06  // Z轴数据高字节

// 配置寄存器
#define QMA6100P_REG_RANGE   0x0F  // 量程设置寄存器
#define QMA6100P_REG_BW_ODR  0x10  // 带宽和输出数据率寄存器
#define QMA6100P_REG_POWER_MANAGE 0x11  // 电源管理寄存器
#define QMA6100P_REG_RESET   0x36  // 软件复位寄存器

// 量程设置值
#define QMA6100P_RANGE_2G    0x01  // ±2g
#define QMA6100P_RANGE_4G    0x02  // ±4g
#define QMA6100P_RANGE_8G    0x04  // ±8g
#define QMA6100P_RANGE_16G   0x08  // ±16g
#define QMA6100P_RANGE_32G   0x0F  // ±32g

// 带宽和低通滤波器设置
#define QMA6100P_BW_100      0x06  // 100Hz带宽
#define QMA6100P_LPF_OFF     0x00  // 低通滤波器关闭

// 电源管理
#define QMA6100P_MCLK_51_2K  0x00  // 主时钟51.2kHz

// 物理常数
#define M_G                  9.8f   // 重力加速度

struct IMUData {
    unsigned long timestamp;
    float accel[3];  // x, y, z 加速度 (QMA6100P只有加速度)
    float gyro[3];   // x, y, z 角速度 (QMA6100P无陀螺仪，设为0)
};

class IMUSensor {
private:
    // 基于官方例程的私有函数
    void qma6100p_write_reg(uint8_t reg, uint8_t data);
    void qma6100p_read_reg(uint8_t reg, uint8_t *buf, uint16_t num);
    void qma6100p_read_raw_xyz(int16_t data[3]);
    void qma6100p_read_acc_xyz(float accdata[3]);
    void acc_get_angle(float accl_in[3], float angle[2]);
    uint8_t qma6100p_init(void);

public:
    void init();
    IMUData read();
    bool isConnected();
    void getEulerAngles(float angles[2]);  // 获取欧拉角（俯仰角、横滚角）
};

#endif

