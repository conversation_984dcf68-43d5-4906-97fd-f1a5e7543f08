#include "sensor_fusion.h"
#include <Arduino.h>

void SensorFusion::init() {
    // 初始化EKF参数 - 针对QMA6100P只有加速度计的情况
    Serial.println("传感器融合初始化完成 (仅使用加速度计数据)");
}

Position SensorFusion::update(TagDetection& tag, IMUData& imu, float altitude) {
    Position pos;
    pos.x = pos.y = pos.z = 0;
    pos.confidence = 0.5f;

    // 基于QMA6100P加速度数据的简单姿态估计
    // 注意：没有陀螺仪数据，只能估计静态姿态
    if (imu.accel[0] != 0 || imu.accel[1] != 0 || imu.accel[2] != 0) {
        // 计算倾斜角度 (基于重力向量)
        float roll = atan2(imu.accel[1], imu.accel[2]) * 180.0 / PI;
        float pitch = atan2(-imu.accel[0], sqrt(imu.accel[1]*imu.accel[1] + imu.accel[2]*imu.accel[2])) * 180.0 / PI;

        Serial.printf("姿态角度: Roll=%.1f°, Pitch=%.1f°\n", roll, pitch);
    }

    // TODO: 实现完整的EKF融合算法
    return pos;
}

void SensorFusion::predict(IMUData& imu, float dt) {
    // TODO: EKF预测步骤
}

void SensorFusion::updateVision(TagDetection& tag) {
    // TODO: 视觉更新步骤
}

void SensorFusion::updateAltitude(float altitude) {
    // TODO: 高度更新步骤
}