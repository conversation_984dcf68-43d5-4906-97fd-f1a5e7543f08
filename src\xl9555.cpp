#include "xl9555.h"

// 基于官方例程的XL9555实现

/**
 * @brief 初始化IO扩展芯片
 * @param 无
 * @retval 无
 */
void xl9555_init(void) {
    pinMode(IIC_INT_PIN, INPUT_PULLUP);  // 配置中断引脚
    // 注意：Wire已经在QMA6100P初始化时设置，这里不重复初始化
    // Wire.begin(IIC_SDA, IIC_SCL, 400000);

    Serial.println("XL9555使用已初始化的I2C总线");

    // 检查XL9555连接
    Wire.beginTransmission(EXIO_ADDR);
    if (Wire.endTransmission() != 0) {
        Serial.printf("XL9555未检测到，I2C地址: 0x%02X\n", EXIO_ADDR);
        return;
    }

    Serial.printf("XL9555检测成功，I2C地址: 0x%02X\n", EXIO_ADDR);

    // 上电先读取一次清除中断标志
    xl9555_read_port(0);
    xl9555_read_port(1);

    Serial.println("XL9555 IO扩展芯片初始化完成");
}

/**
 * @brief 向XL9555相关寄存器写数据
 * @param reg : 寄存器地址
 * @param data : 写入到寄存器的数据
 * @retval 无
 */
void xl9555_write_reg(uint8_t reg, uint8_t data) {
    Wire.beginTransmission(EXIO_ADDR);  // 发送从机的7位器件地址到发送队列
    Wire.write(reg);                    // 发送要写入从机寄存器的地址到发送队列
    Wire.write(data);                   // 发送要写入从机寄存器的数据到发送队列
    Wire.endTransmission();             // IIC发送发送队列的数据(无参数,表示发送stop信号)
}

/**
 * @brief 向XL9555相关寄存器读取数据
 * @param reg : 寄存器地址
 * @retval 寄存器的值 / 0xFF:未接收到数据
 */
uint8_t xl9555_read_reg(uint8_t reg) {
    Wire.beginTransmission(EXIO_ADDR);  // 发送从机的7位器件地址到发送队列
    Wire.write(reg);                    // 发送要读取从机的寄存器地址到发送队列
    Wire.endTransmission(0);            // IIC发送发送队列的数据(传参为0,表示重新发送一个start信号,保持IIC总线有效连接)
    Wire.requestFrom(EXIO_ADDR, 1);     // 主机向从机发送数据请求,并获取到数据

    if (Wire.available() != 0) {        // 得到已经接收到的数据字节数
        return Wire.read();             // 到数据缓冲区读取数据
    }
    return 0xFF;
}

/**
 * @brief 设置XL9555某个IO的模式(输出或输入)
 * @param port_pin: 要设置的IO编号,P0~7或P1~7
 * @param mode : IO_SET_OUTPUT / IO_SET_INPUT
 * @retval 无
 */
void xl9555_io_config(uint16_t port_pin, io_mode_t mode) {
    uint8_t config_reg = 0;
    uint8_t config_value = 0;

    config_reg = xl9555_read_reg(port_pin > XL_PORT0_ALL_PIN ?
                                XL9555_CONFIG_PORT1_REG : XL9555_CONFIG_PORT0_REG);

    if (mode == IO_SET_OUTPUT) {
        config_value = config_reg & (~(port_pin >> (port_pin > XL_PORT0_ALL_PIN ? 8 : 0)));
    } else {
        config_value = config_reg | (port_pin >> (port_pin > XL_PORT0_ALL_PIN ? 8 : 0));
    }

    xl9555_write_reg(port_pin > XL_PORT0_ALL_PIN ? XL9555_CONFIG_PORT1_REG : XL9555_CONFIG_PORT0_REG,
                     config_value);
}

/**
 * @brief 设置XL9555配置为输出功能的IO的输出状态(高电平或低电平)
 * @param port_pin : 已经设置好输出功能的IO编号
 * @param state : IO_SET_LOW / IO_SET_HIGH
 * @retval 无
 */
void xl9555_pin_set(uint16_t port_pin, io_state_t state) {
    uint8_t pin_reg = 0;
    uint8_t pin_value = 0;

    pin_reg = xl9555_read_reg(port_pin > XL_PORT0_ALL_PIN ?
                             XL9555_OUTPUT_PORT1_REG : XL9555_OUTPUT_PORT0_REG);

    if (state == IO_SET_HIGH) {
        pin_value = pin_reg | (port_pin >> (port_pin > XL_PORT0_ALL_PIN ? 8 : 0));
    } else {
        pin_value = pin_reg & (~(port_pin >> (port_pin > XL_PORT0_ALL_PIN ? 8 : 0)));
    }

    xl9555_write_reg(port_pin > XL_PORT0_ALL_PIN ? XL9555_OUTPUT_PORT1_REG : XL9555_OUTPUT_PORT0_REG,
                     pin_value);
}

/**
 * @brief 获取XL9555配置为输入功能的IO的状态(高电平或低电平)
 * @param port_pin : 已经设置好输入功能的IO编号
 * @retval 0低电平 / 1高电平
 */
uint8_t xl9555_get_pin(uint16_t port_pin) {
    uint8_t pin_state = 0;
    uint8_t port_value = 0;

    port_value = xl9555_read_reg(port_pin > XL_PORT0_ALL_PIN ?
                                XL9555_INPUT_PORT1_REG : XL9555_INPUT_PORT0_REG);

    pin_state = port_pin >> (port_pin > XL_PORT0_ALL_PIN ? 8 : 0);
    pin_state = pin_state & port_value;

    return pin_state ? 1 : 0;
}

/**
 * @brief 读取XL9555端口状态
 * @param port : 端口号(0或1)
 * @retval 端口状态值
 */
uint16_t xl9555_read_port(uint8_t port) {
    if (port == 0) {
        return xl9555_read_reg(XL9555_INPUT_PORT0_REG);
    } else {
        return xl9555_read_reg(XL9555_INPUT_PORT1_REG);
    }
}
