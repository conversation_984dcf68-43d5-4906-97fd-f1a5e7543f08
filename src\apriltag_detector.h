#ifndef APRILTAG_DETECTOR_H
#define APRILTAG_DETECTOR_H

#include <Arduino.h>
#include "esp_camera.h"

/**
 * @brief 简化的AprilTag检测结果
 */
struct TagDetection {
    bool detected;          // 是否检测到AprilTag
    int count;             // 检测到的标签数量
    float confidence;      // 检测置信度 (0.0-1.0)
    int center_x, center_y; // 检测到的标签中心位置
};

/**
 * @brief 轻量级AprilTag检测器
 * 专门针对黑白棋盘格中的AprilTag进行优化
 */
class AprilTagDetector {
public:
    AprilTagDetector();
    ~AprilTagDetector();

    /**
     * @brief 初始化检测器
     * @return true 初始化成功，false 初始化失败
     */
    bool init();

    /**
     * @brief 检测图像中的AprilTag
     * @param fb 摄像头帧缓冲区
     * @return 检测结果
     */
    TagDetection detect(camera_fb_t* fb);

    /**
     * @brief 简单的AprilTag检测（仅返回是否检测到）
     * @param fb 摄像头帧缓冲区
     * @return true 检测到AprilTag，false 未检测到
     */
    bool detectSimple(camera_fb_t* fb);

private:
    uint8_t* gray_buffer;   // 灰度图像缓冲区
    int image_width;        // 图像宽度
    int image_height;       // 图像高度
    bool initialized;       // 初始化状态

    // 图像处理函数
    void rgb565ToGray(const uint8_t* rgb565_data, uint8_t* gray_data, int width, int height);
    int detectSquarePatterns(const uint8_t* gray_data, int width, int height);
    bool verifyAprilTagPattern(const uint8_t* gray_data, int x, int y, int size, int width, int height);
};

extern AprilTagDetector apriltag_detector;

#endif

