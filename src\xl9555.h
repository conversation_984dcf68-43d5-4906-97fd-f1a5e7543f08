#ifndef XL9555_H
#define XL9555_H

#include <Arduino.h>
#include <Wire.h>

// 基于官方例程的XL9555定义
#define IIC_SCL 42
#define IIC_SDA 41
#define IIC_INT_PIN 0  // 需要用跳线帽进行连接
#define EXIO_ADDR 0x20  // 7位器件地址

// 器件寄存器
#define XL9555_INPUT_PORT0_REG 0   // 输入P0寄存器
#define XL9555_INPUT_PORT1_REG 1   // 输入P1寄存器
#define XL9555_OUTPUT_PORT0_REG 2  // 输出P0寄存器
#define XL9555_OUTPUT_PORT1_REG 3  // 输出P1寄存器
#define XL9555_INVERSION_PORT0_REG 4  // 极性反转P0寄存器
#define XL9555_INVERSION_PORT1_REG 5  // 极性反转P1寄存器
#define XL9555_CONFIG_PORT0_REG 6  // 配置P0寄存器
#define XL9555_CONFIG_PORT1_REG 7  // 配置P1寄存器

// 引脚定义
#define XL_PIN_P00 0x0001
#define XL_PIN_P01 0x0002
#define XL_PIN_P02 0x0004
#define XL_PIN_P03 0x0008
#define XL_PIN_P04 0x0010
#define XL_PIN_P05 0x0020
#define XL_PIN_P06 0x0040
#define XL_PIN_P07 0x0080
#define XL_PIN_P10 0x0100
#define XL_PIN_P11 0x0200
#define XL_PIN_P12 0x0400
#define XL_PIN_P13 0x0800
#define XL_PIN_P14 0x1000
#define XL_PIN_P15 0x2000
#define XL_PIN_P16 0x4000
#define XL_PIN_P17 0x8000
#define XL_PORT0_ALL_PIN 0x00FF
#define XL_PORT1_ALL_PIN 0xFF00

// IO扩展芯片XL9555的各个IO功能
#define KEY0 XL_PIN_P17      // 按键0引脚 P17
#define KEY1 XL_PIN_P16      // 按键1引脚 P16
#define KEY2 XL_PIN_P15      // 按键2引脚 P15
#define KEY3 XL_PIN_P14      // 按键3引脚 P14
#define SLCD_PWR XL_PIN_P13  // SPI_LCD控制背光引脚 P13
#define SLCD_RST XL_PIN_P12  // SPI_LCD复位引脚 P12
#define CT_RST XL_PIN_P11    // 触摸屏中断引脚 P11
#define LCD_BL XL_PIN_P10    // RGB屏背光控制引脚 P10
#define GBC_KEY XL_PIN_P07   // ATK_MODULE接口KEY引脚 P07
#define GBC_LED XL_PIN_P06   // ATK_MODULE接口LED引脚 P06
#define OV_RESET XL_PIN_P05  // 摄像头复位引脚 P05
#define OV_PWDN XL_PIN_P04   // 摄像头待机引脚 P04
#define BEEP XL_PIN_P03      // 蜂鸣器控制引脚 P03
#define SPK_EN XL_PIN_P02    // 功放使能引脚 P02
#define QMA_INT XL_PIN_P01   // QMA6100P中断引脚 P01
#define AP_INT XL_PIN_P00    // AP3216C中断引脚 P00

// IO模式和状态定义
typedef enum {
    IO_SET_OUTPUT = 0,
    IO_SET_INPUT = 1
} io_mode_t;

typedef enum {
    IO_SET_LOW = 0,
    IO_SET_HIGH = 1
} io_state_t;

// 函数声明
void xl9555_init(void);
void xl9555_write_reg(uint8_t reg, uint8_t data);
uint8_t xl9555_read_reg(uint8_t reg);
void xl9555_io_config(uint16_t port_pin, io_mode_t mode);
void xl9555_pin_set(uint16_t port_pin, io_state_t state);
uint8_t xl9555_get_pin(uint16_t port_pin);
uint16_t xl9555_read_port(uint8_t port);

#endif
