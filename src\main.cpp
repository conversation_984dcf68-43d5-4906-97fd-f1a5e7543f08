#include <Arduino.h>
#include <Wire.h>
#include "esp_camera.h"
#include "apriltag_detector.h"
#include "sensor_fusion.h"
#include "imu_sensor.h"
#include "altitude_sensor.h"
#include "lcd_display.h"

// 正点原子DNESP32S3开发板 OV2640摄像头配置
// 根据提供的引脚映射表重新配置
camera_config_t camera_config = {
    .pin_pwdn = -1,        // PWDN未连接
    .pin_reset = -1,       // RESET未连接
    .pin_xclk = -1,        // XCLK通过LEDC生成（根据官方例程）
    .pin_sccb_sda = 39,    // OV_SDA -> GPIO39 (修复deprecated警告)
    .pin_sccb_scl = 38,    // OV_SCL -> GPIO38 (修复deprecated警告)
    .pin_d7 = 18,          // OV_D7 -> GPIO18 (LCD_G7)
    .pin_d6 = 17,          // OV_D6 -> GPIO17 (LCD_B3)
    .pin_d5 = 16,          // OV_D5 -> GPIO16 (LCD_B4)
    .pin_d4 = 15,          // OV_D4 -> GPIO15 (LCD_B5)
    .pin_d3 = 7,           // OV_D3 -> GPIO7  (LCD_B6)
    .pin_d2 = 6,           // OV_D2 -> GPIO6  (LCD_B7)
    .pin_d1 = 5,           // OV_D1 -> GPIO5  (LCD_CLK)
    .pin_d0 = 4,           // OV_D0 -> GPIO4  (LCD_DE)
    .pin_vsync = 47,       // OV_VSYNC -> GPIO47 (LCD_R5)
    .pin_href = 48,        // OV_HREF -> GPIO48 (LCD_R4)
    .pin_pclk = 45,        // OV_PCLK -> GPIO45 (LCD_R3)
    .xclk_freq_hz = 20000000,
    .ledc_timer = LEDC_TIMER_0,
    .ledc_channel = LEDC_CHANNEL_0,
    .pixel_format = PIXFORMAT_RGB565,  // 使用RGB565格式（根据官方例程）
    .frame_size = FRAMESIZE_QQVGA,   // 160x120，减少内存使用
    .jpeg_quality = 12,
    .fb_count = 1,
    .fb_location = CAMERA_FB_IN_DRAM,  // 使用DRAM而不是PSRAM
    .grab_mode = CAMERA_GRAB_WHEN_EMPTY // 优化抓取模式
};

SensorFusion fusion;
IMUSensor imu;
AltitudeSensor altimeter;

// LED引脚定义
#define LED_PIN 1  // GPIO1 -> LED1 (根据引脚表)

void setup() {
    Serial.begin(115200);
    delay(2000);  // 等待串口稳定
    Serial.println("\n=== 正点原子DNESP32S3 AprilTag系统启动 ===");

    // 初始化LED（根据官方例程，高电平熄灭，低电平点亮）
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, HIGH);  // 初始状态熄灭

    // 显示摄像头配置信息
    Serial.println("摄像头引脚配置:");
    Serial.printf("XCLK: %d, SDA: %d, SCL: %d\n", camera_config.pin_xclk, camera_config.pin_sccb_sda, camera_config.pin_sccb_scl);
    Serial.printf("PCLK: %d, VSYNC: %d, HREF: %d\n", camera_config.pin_pclk, camera_config.pin_vsync, camera_config.pin_href);

    // 先初始化传感器（包含I2C初始化）- 这是关键！
    Serial.println("正在初始化传感器...");
    imu.init();  // 这会初始化I2C总线

    // 初始化摄像头 - 基于官方例程（在I2C初始化之后）
    Serial.println("正在初始化OV2640摄像头...");

    // 通过XL9555控制摄像头电源和复位（现在I2C总线已经可用）
    Serial.println("配置摄像头电源和复位引脚...");

    // 首先确保摄像头断电
    xl9555_io_config(OV_PWDN, IO_SET_OUTPUT);
    xl9555_pin_set(OV_PWDN, IO_SET_HIGH);    // 摄像头断电
    Serial.println("摄像头断电完成");
    delay(100);

    // 配置复位引脚
    xl9555_io_config(OV_RESET, IO_SET_OUTPUT);
    xl9555_pin_set(OV_RESET, IO_SET_LOW);    // 保持复位状态
    Serial.println("摄像头复位引脚配置完成");
    delay(50);

    // 摄像头上电
    xl9555_pin_set(OV_PWDN, IO_SET_LOW);     // 摄像头上电
    Serial.println("摄像头上电完成");
    delay(100);  // 等待电源稳定

    // 释放复位
    xl9555_pin_set(OV_RESET, IO_SET_HIGH);   // 释放复位
    Serial.println("摄像头复位释放完成");
    delay(100);  // 等待摄像头启动

    // 配置XCLK（如果需要的话）
    // 根据正点原子例程，XCLK由esp_camera_init自动配置
    Serial.println("开始初始化摄像头...");

    esp_err_t err = esp_camera_init(&camera_config);

    // 如果第一次失败，尝试重新初始化
    if (err != ESP_OK) {
        Serial.printf("摄像头初始化失败! 错误代码: 0x%x，尝试重新初始化...\n", err);

        // 重新复位摄像头
        xl9555_pin_set(OV_RESET, IO_SET_LOW);    // 复位
        delay(100);
        xl9555_pin_set(OV_RESET, IO_SET_HIGH);   // 释放复位
        delay(200);

        // 第二次尝试
        err = esp_camera_init(&camera_config);
    }

    if (err != ESP_OK) {
        Serial.printf("摄像头初始化最终失败! 错误代码: 0x%x\n", err);
        Serial.println("可能的原因:");
        Serial.println("1. 摄像头模块未正确连接");
        Serial.println("2. 引脚配置错误（与LCD引脚冲突）");
        Serial.println("3. 电源供电不足");
        Serial.println("4. I2C总线冲突");
        Serial.println("继续初始化其他传感器...");
    } else {
        Serial.println("OV2640摄像头初始化完成!");

        // 获取摄像头信息并配置参数
        sensor_t *s = esp_camera_sensor_get();
        if (s) {
            Serial.printf("摄像头ID: 0x%x\n", s->id.PID);
            s->set_brightness(s, 0);   // 设置亮度 (-2 ~ 2)
            s->set_contrast(s, 0);     // 设置对比度 (-2 ~ 2)
            s->set_saturation(s, 0);   // 设置饱和度 (-2 ~ 2)
            s->set_hmirror(s, 0);      // 不设置水平方向翻转
            s->set_vflip(s, 1);        // 设置垂直方向翻转
        }
    }

    // 然后初始化LCD显示屏（使用已初始化的I2C）
    lcd_display.init();

    // 摄像头已经成功初始化，无需重复初始化
    if (err == ESP_OK) {
        Serial.println("摄像头已成功初始化，跳过重复初始化");
    }

    // 初始化其他组件
    // altimeter.init();  // 暂时没有气压计
    apriltag_detector.init();  // 使用全局实例
    fusion.init();

    Serial.println("=== 系统初始化完成 ===\n");
}

void loop() {
    static bool camera_available = true;
    static unsigned long last_camera_check = 0;
    static bool display_mode = false;  // false: 状态显示, true: 图像显示
    static unsigned long last_mode_switch = 0;

    // 帧率统计
    static unsigned long frame_count = 0;
    static unsigned long last_fps_time = 0;
    static float current_fps = 0.0f;

    frame_count++;
    unsigned long current_time = millis();
    if (current_time - last_fps_time >= 1000) {  // 每秒统计一次
        current_fps = frame_count * 1000.0f / (current_time - last_fps_time);
        frame_count = 0;
        last_fps_time = current_time;
    }

    Serial.printf("=== 系统状态检查 (FPS: %.1f) ===\n", current_fps);

    // 每5秒切换一次显示模式
    if (millis() - last_mode_switch > 5000) {
        display_mode = !display_mode;
        last_mode_switch = millis();
        Serial.printf("切换显示模式: %s\n", display_mode ? "图像显示" : "状态显示");
    }

    // 尝试获取图像
    camera_fb_t* fb = nullptr;
    if (camera_available || (millis() - last_camera_check > 5000)) {
        fb = esp_camera_fb_get();
        if (!fb) {
            if (camera_available) {
                Serial.println("摄像头获取失败，切换到仅传感器模式");
                camera_available = false;
            }
            last_camera_check = millis();
        } else {
            if (!camera_available) {
                Serial.println("摄像头恢复正常");
                camera_available = true;
            }
            Serial.printf("图像获取成功: %dx%d, 大小: %d bytes, 格式: %d\n",
                          fb->width, fb->height, fb->len, fb->format);
        }
    }

    // 读取QMA6100P加速度数据
    IMUData imu_data = imu.read();
    Serial.printf("加速度: X=%.3f, Y=%.3f, Z=%.3f g\n",
                  imu_data.accel[0], imu_data.accel[1], imu_data.accel[2]);

    // 使用官方例程的姿态解算
    float euler_angles[2];  // [俯仰角, 横滚角]
    imu.getEulerAngles(euler_angles);
    Serial.printf("姿态角度: 俯仰角=%.1f°, 横滚角=%.1f°\n", euler_angles[0], euler_angles[1]);

    // AprilTag检测
    bool apriltag_detected = false;
    if (fb != nullptr) {
        apriltag_detected = apriltag_detector.detectSimple(fb);
        Serial.printf("AprilTag检测: %s\n", apriltag_detected ? "true" : "false");
    } else {
        // 摄像头不可用时，显示检测失败
        Serial.println("AprilTag检测: false (摄像头不可用)");
    }

    // 更新LCD显示
    if (display_mode && fb != nullptr) {
        // 图像显示模式
        lcd_display.displayCameraImage(fb);
    } else {
        // 状态显示模式
        float acc_array[3] = {imu_data.accel[0], imu_data.accel[1], imu_data.accel[2]};
        lcd_display.updateDisplay(acc_array, euler_angles, (fb != nullptr), apriltag_detected);
    }
    // TODO: 传感器融合 (暂时不使用气压计数据)

    if (fb) {
        esp_camera_fb_return(fb);
    }

    // LED闪烁指示系统运行（根据官方例程：LOW点亮，HIGH熄灭）
    static bool led_state = true;  // true=熄灭，false=点亮
    led_state = !led_state;
    digitalWrite(LED_PIN, led_state ? HIGH : LOW);

    Serial.println("========================\n");
    // 移除delay，释放帧率限制，让系统以最大性能运行
    // delay(1000); // 移除1秒延迟
}

